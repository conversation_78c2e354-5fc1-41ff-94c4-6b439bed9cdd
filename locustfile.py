"""
海博平台骑手位置查询接口性能测试
"""

import random
import time
from locust import HttpUser, task, between
from locust.exception import RescheduleTask

from haibo_api import HaiboAPI
from config import TEST_ORDER_IDS, TEST_CARRIER_SHOP_IDS


class HaiboRiderLocationUser(HttpUser):
    """海博平台骑手位置查询性能测试用户"""
    
    # 用户等待时间：1-3秒之间
    wait_time = between(1, 2)
    
    def on_start(self):
        """测试开始时的初始化"""
        self.api_client = HaiboAPI()
        print(f"用户 {self.environment.runner.user_count} 开始测试")

    
    @task
    def get_rider_location_with_shop_id(self):
        """
        使用订单号和门店ID查询骑手位置（权重：5）
        """
        # 随机选择订单号和门店ID
        order_id = random.choice(TEST_ORDER_IDS)
        carrier_shop_id = random.choice(TEST_CARRIER_SHOP_IDS)
        
        start_time = time.time()
        
        try:
            # 调用API
            response = self.api_client.get_rider_location(order_id, carrier_shop_id)
            
            # 计算响应时间
            response_time = int((time.time() - start_time) * 1000)
            
            # 验证响应
            if self.api_client.validate_response(response):
                # 记录成功请求
                self.environment.events.request.fire(
                    request_type="POST",
                    name="/api/haibo/rider-location (with shopId)",
                    response_time=response_time,
                    response_length=len(response.content),
                    exception=None,
                    context={}
                )
            else:
                # 记录失败请求
                self.environment.events.request.fire(
                    request_type="POST",
                    name="/api/haibo/rider-location (with shopId)",
                    response_time=response_time,
                    response_length=len(response.content),
                    exception=Exception(f"API返回错误: {response.text}"),
                    context={}
                )
                
        except Exception as e:
            # 记录异常
            response_time = int((time.time() - start_time) * 1000)
            self.environment.events.request.fire(
                request_type="POST",
                name="/api/haibo/rider-location (with shopId)",
                response_time=response_time,
                response_length=0,
                exception=e,
                context={}
            )
